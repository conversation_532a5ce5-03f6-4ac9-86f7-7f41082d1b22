package models

import (
	"encoding/json"
	"fmt"
)

type CodeType int

const (
	CodeTypeUnknown CodeType = iota
	CodeTypeForex
	CodeTypeEnergy
	CodeTypeStockHK
	CodeTypeStockUS
	CodeTypeStockA
	CodeTypeCrypto
)

func (c CodeType) MarshalJSON() ([]byte, error) {
	names := [...]string{"forex", "energy", "stock_hk", "stock_us", "stock_a", "crypto"}
	if c < CodeTypeUnknown || c > CodeTypeStockA {
		return nil, fmt.Erro<PERSON>("无效的产品类型: %d", c)
	}
	return json.<PERSON>(names[c])
}

func (c *CodeType) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}

	switch str {
	case "forex":
		*c = CodeTypeForex
	case "energy":
		*c = CodeTypeEnergy
	case "stock_hk":
		*c = CodeTypeStockHK
	case "stock_us":
		*c = CodeTypeStockUS
	case "stock_a":
		*c = CodeTypeStockA
	case "crypto":
		*c = CodeTypeCrypto
	default:
		return fmt.Erro<PERSON>("无效的产品代码: %s", str)
	}
	return nil
}
