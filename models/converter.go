package models

import (
	"fmt"
	"strconv"
	"strings"
)

// TypeToAssetTypeMapping 定义Type和SubType到AssetType的映射关系
var TypeToAssetTypeMapping = map[string]map[string]int{
	"forex": {
		"major":  1, // 主要货币对
		"minor":  2, // 次要货币对
		"exotic": 3, // 奇异货币对
		"":       1, // 默认为主要货币对
	},
	"crypto": {
		"major":  4, // 主流加密货币
		"alt":    5, // 山寨币
		"stable": 6, // 稳定币
		"":       4, // 默认为主流加密货币
	},
	"stock": {
		"us":  7,  // 美股
		"hk":  8,  // 港股
		"a":   9,  // A股
		"":    7,  // 默认为美股
	},
	"energy": {
		"oil":    10, // 原油
		"gas":    11, // 天然气
		"metal":  12, // 贵金属
		"":       10, // 默认为原油
	},
}

// AssetTypeToTypeMapping 定义AssetType到Type和SubType的反向映射
var AssetTypeToTypeMapping = map[int]struct {
	Type    string
	SubType string
}{
	1:  {"forex", "major"},
	2:  {"forex", "minor"},
	3:  {"forex", "exotic"},
	4:  {"crypto", "major"},
	5:  {"crypto", "alt"},
	6:  {"crypto", "stable"},
	7:  {"stock", "us"},
	8:  {"stock", "hk"},
	9:  {"stock", "a"},
	10: {"energy", "oil"},
	11: {"energy", "gas"},
	12: {"energy", "metal"},
}

// ToStorage 将ProductAPI转换为ProductStorage格式
func (p *ProductAPI) ToStorage() *ProductStorage {
	storage := &ProductStorage{
		Code:      p.Code,
		SymbolId:  p.SymbolId,
		TradeType: p.TradeType,
		TradeMode: p.TradeMode,
		AssetType: p.AssetType,
	}

	// 如果AssetType为0且有Type/SubType信息，则根据映射计算AssetType
	if storage.AssetType == 0 && p.Type != "" {
		if typeMap, exists := TypeToAssetTypeMapping[p.Type]; exists {
			if assetType, exists := typeMap[p.SubType]; exists {
				storage.AssetType = assetType
			} else if assetType, exists := typeMap[""]; exists {
				// 使用默认值
				storage.AssetType = assetType
			}
		}
	}

	return storage
}

// ToAPI 将ProductStorage转换为ProductAPI格式
func (p *ProductStorage) ToAPI() *ProductAPI {
	api := &ProductAPI{
		Code:      p.Code,
		SymbolId:  p.SymbolId,
		TradeType: p.TradeType,
		TradeMode: p.TradeMode,
		AssetType: p.AssetType,
		IsActive:  true, // 默认为激活状态
	}

	// 根据AssetType反向映射Type和SubType
	if mapping, exists := AssetTypeToTypeMapping[p.AssetType]; exists {
		api.Type = mapping.Type
		api.SubType = mapping.SubType
	}

	return api
}

// ToStorageString 将ProductStorage转换为ETCD存储的字符串格式
// 格式: "code:symboldid:tradetype:trademode:assettype"
func (p *ProductStorage) ToStorageString() string {
	return fmt.Sprintf("%s:%s:%d:%d:%d",
		p.Code,
		p.SymbolId,
		p.TradeType,
		p.TradeMode,
		p.AssetType,
	)
}

// FromStorageString 从ETCD存储的字符串格式解析为ProductStorage
func FromStorageString(data string) (*ProductStorage, error) {
	parts := strings.Split(data, ":")
	if len(parts) != 5 {
		return nil, fmt.Errorf("invalid storage format: expected 5 parts, got %d", len(parts))
	}

	tradeType, err := strconv.Atoi(parts[2])
	if err != nil {
		return nil, fmt.Errorf("invalid trade_type: %s", parts[2])
	}

	tradeMode, err := strconv.Atoi(parts[3])
	if err != nil {
		return nil, fmt.Errorf("invalid trade_mode: %s", parts[3])
	}

	assetType, err := strconv.Atoi(parts[4])
	if err != nil {
		return nil, fmt.Errorf("invalid asset_type: %s", parts[4])
	}

	return &ProductStorage{
		Code:      parts[0],
		SymbolId:  parts[1],
		TradeType: tradeType,
		TradeMode: tradeMode,
		AssetType: assetType,
	}, nil
}

// ConvertAPIToStorage 便利函数：直接从ProductAPI转换为存储字符串
func ConvertAPIToStorage(api *ProductAPI) string {
	storage := api.ToStorage()
	return storage.ToStorageString()
}

// ConvertStorageToAPI 便利函数：直接从存储字符串转换为ProductAPI
func ConvertStorageToAPI(data string) (*ProductAPI, error) {
	storage, err := FromStorageString(data)
	if err != nil {
		return nil, err
	}
	return storage.ToAPI(), nil
}

// ValidateTypeSubType 验证Type和SubType的组合是否有效
func ValidateTypeSubType(productType, subType string) error {
	if productType == "" {
		return nil // Type为空是允许的
	}

	typeMap, exists := TypeToAssetTypeMapping[productType]
	if !exists {
		return fmt.Errorf("invalid product type: %s", productType)
	}

	if subType != "" {
		if _, exists := typeMap[subType]; !exists {
			return fmt.Errorf("invalid sub_type '%s' for type '%s'", subType, productType)
		}
	}

	return nil
}

// GetAssetTypeFromTypeSubType 根据Type和SubType获取AssetType
func GetAssetTypeFromTypeSubType(productType, subType string) (int, error) {
	if productType == "" {
		return 0, fmt.Errorf("product type cannot be empty")
	}

	typeMap, exists := TypeToAssetTypeMapping[productType]
	if !exists {
		return 0, fmt.Errorf("invalid product type: %s", productType)
	}

	if assetType, exists := typeMap[subType]; exists {
		return assetType, nil
	}

	// 使用默认值
	if assetType, exists := typeMap[""]; exists {
		return assetType, nil
	}

	return 0, fmt.Errorf("no asset type mapping found for type: %s, sub_type: %s", productType, subType)
}
