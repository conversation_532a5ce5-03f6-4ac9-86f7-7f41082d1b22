package models

// <PERSON><PERSON><PERSON><PERSON> represents an API key configuration
type A<PERSON><PERSON>ey struct {
	Name               string     `json:"name" validate:"required"`
	Key                string     `json:"key" validate:"required"`
	Secret             string     `json:"secret" validate:"required"`
	Token              string     `json:"token" validate:"required"`
	Capacity           int        `json:"capacity"`
	Capbility          []CodeType `json:"capbility"` // 怎样配置jaon枚举类型
	AssignedReceivers  []string   `json:"associated_receivers"`
	AssociatedProducts []string   `json:"associated_products"`
	// 其他相关字段
}

// Product represents a product configuration
type Product struct {
	Code      string `json:"code"`
	SymbolId  string `json:"symbol_id"`
	TradeType int `json:"trade_type"`
	TradeMode int `json:"trade_mode"`
	AssetType int    `json:"asset_type"`

	// 其他相关字段
}

// Products 用于批量产品操作
type Products struct {
	Items []Product `json:"items"`
}

// Receiver represents a receiver instance configuration
type Receiver struct {
	ID               string   `json:"id"`
	IP               string   `json:"ip" validate:"required"`
	Port             int      `json:"port" validate:"required"`
	AssignedProducts []string `json:"assigned_products"`
	Capacity         int      `json:"capacity"` // Receiver 的容量，可以处理的产品数量
	// 其他相关字段
}

// HttpApi represents an http-api instance configuration
type HttpApi struct {
	IP string `json:"ip"`
	// 其他相关字段
}

// WebsocketApi represents a websocket-api instance configuration
type WebsocketApi struct {
	IP string `json:"ip"`
	// 其他相关字段
}

// ProductsBatchReq 用于批量增删产品的运维请求
type ProductsBatchReq struct {
	Type    CodeType   `json:"type" validate:"required"`
	Codes   []string `json:"codes" validate:"required"`
}
