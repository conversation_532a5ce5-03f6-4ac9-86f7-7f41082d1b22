package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"
)

// GetReceivers retrieves all receivers from local cache
func (s *ConfigService) GetReceivers() ([]*models.Receiver, error) {
	var receivers []*models.Receiver
	for _, receiver := range s.receiversCache {
		receivers = append(receivers, receiver)
	}
	return receivers, nil
}

// CreateReceiver creates a new receiver in etcd and updates local cache
func (s *ConfigService) CreateReceiver(receiver *models.Receiver) error {
	if err := s.validate.Struct(receiver); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := receiversPrefix + receiver.ID
	value, err := json.Marshal(receiver)
	if err != nil {
		return fmt.Errorf("failed to marshal receiver: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put receiver to etcd: %w", err)
	}
	s.receiversCache[receiver.ID] = receiver
	return nil
}

// UpdateReceiver updates an existing receiver in etcd and local cache
func (s *ConfigService) UpdateReceiver(receiver *models.Receiver) error {
	if err := s.validate.Struct(receiver); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := receiversPrefix + receiver.ID
	value, err := json.Marshal(receiver)
	if err != nil {
		return fmt.Errorf("failed to marshal receiver: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put receiver to etcd: %w", err)
	}
	s.receiversCache[receiver.ID] = receiver
	return nil
}

// DeleteReceiver deletes a receiver from etcd and local cache
func (s *ConfigService) DeleteReceiver(id string) error {
	if id == "" {
		return fmt.Errorf("receiver id cannot be empty")
	}
	key := receiversPrefix + id
	_, err := s.etcdClient.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("failed to delete receiver from etcd: %w", err)
	}
	delete(s.receiversCache, id)
	return nil
}

// GetReceiver retrieves a specific receiver by id from local cache
func (s *ConfigService) GetReceiver(id string) (*models.Receiver, error) {
	if id == "" {
		return nil, fmt.Errorf("receiver id cannot be empty")
	}
	receiver, ok := s.receiversCache[id]
	if !ok {
		return nil, fmt.Errorf("receiver not found: %s", id)
	}
	return receiver, nil
}
