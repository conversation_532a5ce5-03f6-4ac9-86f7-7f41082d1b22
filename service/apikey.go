package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"
)

// GetApikeys retrieves all apikeys from local cache
func (s *ConfigService) GetApikeys() ([]*models.ApiKey, error) {
	var apikeys []*models.ApiKey
	for _, apikey := range s.apikeysCache {
		apikeys = append(apikeys, apikey)
	}
	return apikeys, nil
}

// CreateApiKey creates a new apikey in etcd and updates local cache
func (s *ConfigService) CreateApiKey(apikey *models.ApiKey) error {
	if err := s.validate.Struct(apikey); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := apikeysPrefix + apikey.Name
	value, err := json.Marshal(apikey)
	if err != nil {
		return fmt.Errorf("failed to marshal apikey: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put apikey to etcd: %w", err)
	}
	s.apikeysCache[apikey.Name] = apikey
	return nil
}
