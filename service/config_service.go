package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-playground/validator/v10"
	clientv3 "go.etcd.io/etcd/client/v3"
)

const (
	configPrefix    = "/alltick/"
	apikeysPrefix   = configPrefix + "apikeys/"
	productsPrefix  = configPrefix + "products/"
	receiversPrefix = configPrefix + "receivers/"
	// ... other prefixes
)

// ConfigService handles configuration logic
type ConfigService struct {
	etcdClient *clientv3.Client
	validate   *validator.Validate
	// 本地缓存
	apikeysCache   map[string]*models.ApiKey
	productsCache  map[string]*models.Product
	receiversCache map[string]*models.Receiver
}

// NewConfigService creates a new ConfigService
func NewConfigService(client *clientv3.Client) *ConfigService {
	cs := &ConfigService{
		etcdClient:     client,
		validate:       validator.New(),
		apikeysCache:   make(map[string]*models.ApiKey),
		productsCache:  make(map[string]*models.Product),
		receiversCache: make(map[string]*models.Receiver),
	}
	// 初始化时从etcd加载数据到本地缓存
	cs.loadCacheFromEtcd()
	return cs
}

// loadCacheFromEtcd loads data from etcd into local cache
func (s *ConfigService) loadCacheFromEtcd() {
	// 加载apikeys
	resp, err := s.etcdClient.Get(context.Background(), apikeysPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var apikey models.ApiKey
			if err := json.Unmarshal(kv.Value, &apikey); err == nil {
				s.apikeysCache[apikey.Name] = &apikey
			}
		}
	}
	// 加载products
	resp, err = s.etcdClient.Get(context.Background(), productsPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var product models.Product
			if err := json.Unmarshal(kv.Value, &product); err == nil {
				s.productsCache[product.Code] = &product
			}
		}
	}
	// 加载receivers
	resp, err = s.etcdClient.Get(context.Background(), receiversPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var receiver models.Receiver
			if err := json.Unmarshal(kv.Value, &receiver); err == nil {
				s.receiversCache[receiver.ID] = &receiver
			}
		}
	}
}

// GetApikeys retrieves all apikeys from local cache
func (s *ConfigService) GetApikeys() ([]*models.ApiKey, error) {
	var apikeys []*models.ApiKey
	for _, apikey := range s.apikeysCache {
		apikeys = append(apikeys, apikey)
	}
	return apikeys, nil
}

// CreateApiKey creates a new apikey in etcd and updates local cache
func (s *ConfigService) CreateApiKey(apikey *models.ApiKey) error {
	if err := s.validate.Struct(apikey); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := apikeysPrefix + apikey.Name
	value, err := json.Marshal(apikey)
	if err != nil {
		return fmt.Errorf("failed to marshal apikey: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put apikey to etcd: %w", err)
	}
	s.apikeysCache[apikey.Name] = apikey
	return nil
}

// GetProducts retrieves all products from local cache
func (s *ConfigService) GetProducts() ([]*models.Product, error) {
	var products []*models.Product
	for _, product := range s.productsCache {
		products = append(products, product)
	}
	return products, nil
}

// CreateProduct creates a new product in etcd and updates local cache
func (s *ConfigService) CreateProduct(product *models.Product) error {
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := productsPrefix + product.Code
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	s.productsCache[product.Code] = product
	return nil
}

// UpdateProduct updates an existing product in etcd and local cache
func (s *ConfigService) UpdateProduct(product *models.Product) error {
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := productsPrefix + product.Code
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	s.productsCache[product.Code] = product
	return nil
}

// DeleteProduct deletes a product from etcd and local cache
func (s *ConfigService) DeleteProduct(name string) error {
	if name == "" {
		return fmt.Errorf("product name cannot be empty")
	}
	key := productsPrefix + name
	_, err := s.etcdClient.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("failed to delete product from etcd: %w", err)
	}
	delete(s.productsCache, name)
	return nil
}

// GetProduct retrieves a specific product by name from local cache
func (s *ConfigService) GetProduct(name string) (*models.Product, error) {
	if name == "" {
		return nil, fmt.Errorf("product name cannot be empty")
	}
	product, ok := s.productsCache[name]
	if !ok {
		return nil, fmt.Errorf("product not found: %s", name)
	}
	return product, nil
}

// ... Implement dynamic allocation logic here
// WatchReceiverConfig watches for configuration changes for a specific receiver
func (s *ConfigService) WatchReceiverConfig(receiverID string, callback func([]string)) error {
	watchKey := fmt.Sprintf("/assignments/product_receiver/%s", receiverID)
	rch := s.etcdClient.Watch(context.Background(), watchKey, clientv3.WithPrefix())
	go func() {
		for wresp := range rch {
			for _, ev := range wresp.Events {
				// 根据事件类型和新的值更新 receiver 的产品列表
				// 这里需要更复杂的逻辑来处理不同的事件类型和键
				fmt.Printf("Receiver %s config changed: EventType: %s, Key: %s, Value: %s\n", receiverID, ev.Type, ev.Kv.Key, ev.Kv.Value)
				// 触发 callback，通知 receiver 更新本地配置
				// callback(最新的产品列表)
			}
		}
	}()
	return nil
}

// GetReceivers retrieves all receivers from local cache
func (s *ConfigService) GetReceivers() ([]*models.Receiver, error) {
	var receivers []*models.Receiver
	for _, receiver := range s.receiversCache {
		receivers = append(receivers, receiver)
	}
	return receivers, nil
}

// CreateReceiver creates a new receiver in etcd and updates local cache
func (s *ConfigService) CreateReceiver(receiver *models.Receiver) error {
	if err := s.validate.Struct(receiver); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := receiversPrefix + receiver.ID
	value, err := json.Marshal(receiver)
	if err != nil {
		return fmt.Errorf("failed to marshal receiver: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put receiver to etcd: %w", err)
	}
	s.receiversCache[receiver.ID] = receiver
	return nil
}

// UpdateReceiver updates an existing receiver in etcd and local cache
func (s *ConfigService) UpdateReceiver(receiver *models.Receiver) error {
	if err := s.validate.Struct(receiver); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	key := receiversPrefix + receiver.ID
	value, err := json.Marshal(receiver)
	if err != nil {
		return fmt.Errorf("failed to marshal receiver: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put receiver to etcd: %w", err)
	}
	s.receiversCache[receiver.ID] = receiver
	return nil
}

// DeleteReceiver deletes a receiver from etcd and local cache
func (s *ConfigService) DeleteReceiver(id string) error {
	if id == "" {
		return fmt.Errorf("receiver id cannot be empty")
	}
	key := receiversPrefix + id
	_, err := s.etcdClient.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("failed to delete receiver from etcd: %w", err)
	}
	delete(s.receiversCache, id)
	return nil
}

// GetReceiver retrieves a specific receiver by id from local cache
func (s *ConfigService) GetReceiver(id string) (*models.Receiver, error) {
	if id == "" {
		return nil, fmt.Errorf("receiver id cannot be empty")
	}
	receiver, ok := s.receiversCache[id]
	if !ok {
		return nil, fmt.Errorf("receiver not found: %s", id)
	}
	return receiver, nil
}

// AssignProductsToReceivers 实现产品分配到receiver的加权均衡算法
// 1. 获取所有需要分配的产品列表（排除已静态配置的产品）
// 2. 获取所有可用的 receiver 列表
// 3. 根据每个 apikey 关联的产品数量，以及每个 receiver 当前负责的产品数量，进行加权分配。目标是让每个 receiver 负责的产品数量相对均衡，并且尽量减少配置变动。
// 4. 优先将产品分配给那些容量还未达到上限的 receiver。
// 5. 如果某个 receiver 删除了产品，下次分配时优先考虑将新的产品分配给它，以填补空缺。
func (s *ConfigService) AssignProductsToReceivers(staticProducts map[string]bool) error {
	// 1. 获取所有需要分配的产品列表（排除已静态配置的产品）
	var toAssign []string
	for name := range s.productsCache {
		if !staticProducts[name] {
			toAssign = append(toAssign, name)
		}
	}
	if len(toAssign) == 0 {
		return nil
	}
	// 2. 获取所有可用的 receiver 列表
	receivers := []*models.Receiver{}
	for _, r := range s.receiversCache {
		receivers = append(receivers, r)
	}
	if len(receivers) == 0 {
		return fmt.Errorf("no available receivers")
	}
	// 3. 统计每个receiver已分配产品数量
	type recStat struct {
		r     *models.Receiver
		count int
	}
	recStats := make([]*recStat, 0, len(receivers))
	for _, r := range receivers {
		recStats = append(recStats, &recStat{r: r, count: len(r.AssignedProducts)})
	}
	// 4. 分配产品，优先分配给容量未满且产品最少的receiver
	for _, prod := range toAssign {
		// 找到容量未满且产品最少的receiver
		var best *recStat
		for _, stat := range recStats {
			if stat.count < stat.r.Capacity {
				if best == nil || stat.count < best.count {
					best = stat
				}
			}
		}
		if best == nil {
			return fmt.Errorf("no receiver has available capacity for product %s", prod)
		}
		// 分配
		best.r.AssignedProducts = append(best.r.AssignedProducts, prod)
		best.count++
	}
	// 5. 更新receiver到etcd和本地缓存
	for _, stat := range recStats {
		if err := s.UpdateReceiver(stat.r); err != nil {
			return fmt.Errorf("update receiver %s failed: %w", stat.r.ID, err)
		}
	}
	return nil
}
