package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-playground/validator/v10"
	clientv3 "go.etcd.io/etcd/client/v3"
)

const (
	configPrefix    = "/alltick/"
	apikeysPrefix   = configPrefix + "apikeys/"
	productsPrefix  = configPrefix + "products/"
	receiversPrefix = configPrefix + "receivers/"
	// ... other prefixes
)

// ConfigService handles configuration logic
type ConfigService struct {
	etcdClient *clientv3.Client
	validate   *validator.Validate
	// 本地缓存
	apikeysCache   map[string]*models.ApiKey
	productsCache  map[string]*models.Product
	receiversCache map[string]*models.Receiver
}

// NewConfigService creates a new ConfigService
func NewConfigService(client *clientv3.Client) *ConfigService {
	cs := &ConfigService{
		etcdClient:     client,
		validate:       validator.New(),
		apikeysCache:   make(map[string]*models.ApiKey),
		productsCache:  make(map[string]*models.Product),
		receiversCache: make(map[string]*models.Receiver),
	}
	// 初始化时从etcd加载数据到本地缓存
	cs.loadCacheFromEtcd()
	return cs
}

// loadCacheFromEtcd loads data from etcd into local cache
func (s *ConfigService) loadCacheFromEtcd() {
	// 加载apikeys
	resp, err := s.etcdClient.Get(context.Background(), apikeysPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var apikey models.ApiKey
			if err := json.Unmarshal(kv.Value, &apikey); err == nil {
				s.apikeysCache[apikey.Name] = &apikey
			}
		}
	}
	// 加载products
	resp, err = s.etcdClient.Get(context.Background(), productsPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var product models.Product
			if err := json.Unmarshal(kv.Value, &product); err == nil {
				s.productsCache[product.Code] = &product
			}
		}
	}
	// 加载receivers
	resp, err = s.etcdClient.Get(context.Background(), receiversPrefix, clientv3.WithPrefix())
	if err == nil {
		for _, kv := range resp.Kvs {
			var receiver models.Receiver
			if err := json.Unmarshal(kv.Value, &receiver); err == nil {
				s.receiversCache[receiver.ID] = &receiver
			}
		}
	}
}




// ... Implement dynamic allocation logic here
// WatchReceiverConfig watches for configuration changes for a specific receiver
func (s *ConfigService) WatchReceiverConfig(receiverID string, callback func([]string)) error {
	watchKey := fmt.Sprintf("/assignments/product_receiver/%s", receiverID)
	rch := s.etcdClient.Watch(context.Background(), watchKey, clientv3.WithPrefix())
	go func() {
		for wresp := range rch {
			for _, ev := range wresp.Events {
				// 根据事件类型和新的值更新 receiver 的产品列表
				// 这里需要更复杂的逻辑来处理不同的事件类型和键
				fmt.Printf("Receiver %s config changed: EventType: %s, Key: %s, Value: %s\n", receiverID, ev.Type, ev.Kv.Key, ev.Kv.Value)
				// 触发 callback，通知 receiver 更新本地配置
				// callback(最新的产品列表)
			}
		}
	}()
	return nil
}



// AssignProductsToReceivers 实现产品分配到receiver的加权均衡算法
// 1. 获取所有需要分配的产品列表（排除已静态配置的产品）
// 2. 获取所有可用的 receiver 列表
// 3. 根据每个 apikey 关联的产品数量，以及每个 receiver 当前负责的产品数量，进行加权分配。目标是让每个 receiver 负责的产品数量相对均衡，并且尽量减少配置变动。
// 4. 优先将产品分配给那些容量还未达到上限的 receiver。
// 5. 如果某个 receiver 删除了产品，下次分配时优先考虑将新的产品分配给它，以填补空缺。
func (s *ConfigService) AssignProductsToReceivers(staticProducts map[string]bool) error {
	// 1. 获取所有需要分配的产品列表（排除已静态配置的产品）
	var toAssign []string
	for name := range s.productsCache {
		if !staticProducts[name] {
			toAssign = append(toAssign, name)
		}
	}
	if len(toAssign) == 0 {
		return nil
	}
	// 2. 获取所有可用的 receiver 列表
	receivers := []*models.Receiver{}
	for _, r := range s.receiversCache {
		receivers = append(receivers, r)
	}
	if len(receivers) == 0 {
		return fmt.Errorf("no available receivers")
	}
	// 3. 统计每个receiver已分配产品数量
	type recStat struct {
		r     *models.Receiver
		count int
	}
	recStats := make([]*recStat, 0, len(receivers))
	for _, r := range receivers {
		recStats = append(recStats, &recStat{r: r, count: len(r.AssignedProducts)})
	}
	// 4. 分配产品，优先分配给容量未满且产品最少的receiver
	for _, prod := range toAssign {
		// 找到容量未满且产品最少的receiver
		var best *recStat
		for _, stat := range recStats {
			if stat.count < stat.r.Capacity {
				if best == nil || stat.count < best.count {
					best = stat
				}
			}
		}
		if best == nil {
			return fmt.Errorf("no receiver has available capacity for product %s", prod)
		}
		// 分配
		best.r.AssignedProducts = append(best.r.AssignedProducts, prod)
		best.count++
	}
	// 5. 更新receiver到etcd和本地缓存
	for _, stat := range recStats {
		if err := s.UpdateReceiver(stat.r); err != nil {
			return fmt.Errorf("update receiver %s failed: %w", stat.r.ID, err)
		}
	}
	return nil
}
