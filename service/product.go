package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"
)

// GetProducts retrieves all products from local cache
func (s *ConfigService) GetProducts() ([]*models.Product, error) {
	var products []*models.Product
	for _, product := range s.productsCache {
		products = append(products, product)
	}
	return products, nil
}

// CreateProduct creates a new product in etcd and updates local cache
func (s *ConfigService) CreateProduct(product *models.Product) error {
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}
	pType := product.Type
	if product.SubType != "" {
		pType += "/" + product.SubType
	}
	key := productsPrefix + pType + "/" + product.Code
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	s.productsCache[product.Code] = product
	return nil
}

// UpdateProduct updates an existing product in etcd and local cache
func (s *ConfigService) UpdateProduct(product *models.Product) error {
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
		}
	pType := product.Type
	if product.SubType != "" {
		pType += "/" + product.SubType
	}
	key := productsPrefix + pType + "/" + product.Code
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	s.productsCache[product.Code] = product
	return nil
}

// DeleteProduct deletes a product from etcd and local cache
func (s *ConfigService) DeleteProduct(name string) error {
	if name == "" {
		return fmt.Errorf("product name cannot be empty")
	}
	key := productsPrefix + name
	_, err := s.etcdClient.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("failed to delete product from etcd: %w", err)
	}
	delete(s.productsCache, name)
	return nil
}

// GetProduct retrieves a specific product by name from local cache
func (s *ConfigService) GetProduct(name string) (*models.Product, error) {
	if name == "" {
		return nil, fmt.Errorf("product name cannot be empty")
	}
	product, ok := s.productsCache[name]
	if !ok {
		return nil, fmt.Errorf("product not found: %s", name)
	}
	return product, nil
}

// GetProductsByType retrieves products by type from local cache
func (s *ConfigService) GetProductsByType(productType string) ([]*models.Product, error) {
	if productType == "" {
		return nil, fmt.Errorf("product type cannot be empty")
	}
	var products []*models.Product
	for _, product := range s.productsCache {
		if product.Type == productType {
			products = append(products, product)
		}
	}
	return products, nil
}

// GetProductsByTypeAndSubType retrieves products by type and subtype from local cache
func (s *ConfigService) GetProductsByTypeAndSubType(productType, subType string) ([]*models.Product, error) {
	if productType == "" {
		return nil, fmt.Errorf("product type cannot be empty")
	}
	if subType == "" {
		return nil, fmt.Errorf("product subtype cannot be empty")
	}
	var products []*models.Product
	for _, product := range s.productsCache {
		if product.Type == productType && product.SubType == subType {
			products = append(products, product)
		}
	}
	return products, nil
}

// GetProductsByAssetType retrieves products by asset type from local cache (兼容现有的 AssetType 字段)
func (s *ConfigService) GetProductsByAssetType(assetType int) ([]*models.Product, error) {
	var products []*models.Product
	for _, product := range s.productsCache {
		if product.AssetType == assetType {
			products = append(products, product)
		}
	}
	return products, nil
}
