package api

import (
	"alltick_conf_manager/models"
	"alltick_conf_manager/service"
	"encoding/json"
	"log"
	"net/http"
	"strings"

	"github.com/go-playground/validator/v10"
)

func HandleProducts(cfgService *service.ConfigService) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Set content type for JSON responses
		w.<PERSON>er().Set("Content-Type", "application/json")

		switch r.Method {
		case "GET":
			handleGetProducts(w, r, cfgService)
		case "POST":
			handleCreateProduct(w, r, cfgService)
		case "PUT":
			handleUpdateProduct(w, r, cfgService)
		case "DELETE":
			handleDeleteProduct(w, r, cfgService)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

func handleGetProducts(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	// Check if requesting a specific product by name
	productName := strings.TrimPrefix(r.URL.Path, "/products/")
	if productName != "" && productName != "/products" {
		// Get specific product
		product, err := cfgService.GetProduct(productName)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				http.Error(w, err.Error(), http.StatusNotFound)
				return
			}
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		json.NewEncoder(w).Encode(product)
		return
	}

	log.Println("Get all products")
	// Get all products
	products, err := cfgService.GetProducts()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(products)
}

func handleCreateProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	var productsWrapper models.Products
	if err := json.NewDecoder(r.Body).Decode(&productsWrapper); err == nil && len(productsWrapper.Items) > 0 {
		// 批量添加
		var failed []string
		for _, product := range productsWrapper.Items {
			if err := cfgService.CreateProduct(&product); err != nil {
				failed = append(failed, product.Code+":"+err.Error())
			}
		}
		if len(failed) > 0 {
			w.WriteHeader(http.StatusBadRequest)
			json.NewEncoder(w).Encode(map[string]interface{}{"message": "Some products failed", "failed": failed})
			return
		}
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]string{"message": "Products created successfully"})
		return
	}
	// 兼容单个Product
	var product models.Product
	if err := json.NewDecoder(r.Body).Decode(&product); err != nil {
		http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
		return
	}
	if err := cfgService.CreateProduct(&product); err != nil {
		if _, ok := err.(validator.ValidationErrors); ok {
			http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
			return
		}
		if strings.Contains(err.Error(), "validation failed") {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]string{"message": "Product created successfully"})
}

func handleUpdateProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	var product models.Product
	if err := json.NewDecoder(r.Body).Decode(&product); err != nil {
		http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
		return
	}

	if err := cfgService.UpdateProduct(&product); err != nil {
		// Check if it's a validation error
		if _, ok := err.(validator.ValidationErrors); ok {
			http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
			return
		}
		if strings.Contains(err.Error(), "validation failed") {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]string{"message": "Product updated successfully"})
}

func handleDeleteProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	var productsWrapper models.Products
	if err := json.NewDecoder(r.Body).Decode(&productsWrapper); err == nil && len(productsWrapper.Items) > 0 {
		// 批量删除
		var failed []string
		for _, product := range productsWrapper.Items {
			if err := cfgService.DeleteProduct(product.Code); err != nil {
				failed = append(failed, product.Code+":"+err.Error())
			}
		}
		if len(failed) > 0 {
			w.WriteHeader(http.StatusInternalServerError)
			json.NewEncoder(w).Encode(map[string]interface{}{"message": "Some products failed to delete", "failed": failed})
			return
		}
		json.NewEncoder(w).Encode(map[string]string{"message": "Products deleted successfully"})
		return
	}
	// 兼容单个Product
	productName := strings.TrimPrefix(r.URL.Path, "/products/")
	if productName == "" || productName == "products" {
		http.Error(w, "Product name is required for deletion", http.StatusBadRequest)
		return
	}
	if err := cfgService.DeleteProduct(productName); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(map[string]string{"message": "Product deleted successfully"})
}

func HandleBatchProducts(cfgService *service.ConfigService) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		switch r.Method {
		case "POST":
			var req models.ProductsBatchReq
			if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
				http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
				return
			}
			var failed []string
			for _, code := range req.Codes {
				p := models.Product{
					Code:      code,
					SymbolId:  code,
					TradeType: 1,
					TradeMode: 1,
				}
				if err := cfgService.CreateProduct(&p); err != nil {
					failed = append(failed, code)
				}
			}
			if len(failed) > 0 {
				http.Error(w, "Failed to add products: "+strings.Join(failed, ","), http.StatusInternalServerError)
				return
			}
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(map[string]string{"message": "Products created successfully"})
		case "DELETE":
			var req models.ProductsBatchReq
			if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
				http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
				return
			}
			var failed []string
			for _, code := range req.Codes {
				if err := cfgService.DeleteProduct(code); err != nil {
					failed = append(failed, code)
				}
			}
			if len(failed) > 0 {
				http.Error(w, "Failed to delete products: "+strings.Join(failed, ","), http.StatusInternalServerError)
				return
			}
			json.NewEncoder(w).Encode(map[string]string{"message": "Products deleted successfully"})
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}
