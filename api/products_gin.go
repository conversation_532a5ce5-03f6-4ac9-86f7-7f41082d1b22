package api

import (
	"alltick_conf_manager/models"
	"alltick_conf_manager/service"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// SetupProductRoutes 设置产品相关的路由
func SetupProductRoutes(router *gin.Engine, cfgService *service.ConfigService) {
	productGroup := router.Group("/products")
	{
		productGroup.GET("", GetAllProducts(cfgService))                                          // 获取所有产品
		productGroup.GET("/:code", GetProduct(cfgService))                                        // 获取单个产品
		productGroup.GET("/type/:type", GetProductsByType(cfgService))                            // 按类型查询产品
		productGroup.GET("/type/:type/subtype/:subtype", GetProductsByTypeAndSubType(cfgService)) // 按类型和子类型查询产品
		productGroup.GET("/asset-type/:assetType", GetProductsByAssetType(cfgService))            // 按资产类型查询产品（兼容现有字段）

		productGroup.POST("", CreateProduct(cfgService))               // 创建产品
		productGroup.PUT("/:code", UpdateProduct(cfgService))          // 更新产品
		productGroup.DELETE("/:code", DeleteProduct(cfgService))       // 删除产品
		productGroup.POST("/batch", BatchCreateProducts(cfgService))   // 批量创建产品
		productGroup.DELETE("/batch", BatchDeleteProducts(cfgService)) // 批量删除产品
	}
}

// GetAllProducts 获取所有产品
func GetAllProducts(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		products, err := cfgService.GetProducts()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": products})
	}
}

// GetProduct 获取单个产品
func GetProduct(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Param("code")
		if code == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product code is required"})
			return
		}

		product, err := cfgService.GetProduct(code)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": product})
	}
}

// GetProductsByType 按类型查询产品
func GetProductsByType(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		productType := c.Param("type")
		if productType == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product type is required"})
			return
		}

		products, err := cfgService.GetProductsByType(productType)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": products, "count": len(products)})
	}
}

// GetProductsByTypeAndSubType 按类型和子类型查询产品
func GetProductsByTypeAndSubType(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		productType := c.Param("type")
		subType := c.Param("subtype")

		if productType == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product type is required"})
			return
		}
		if subType == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product subtype is required"})
			return
		}

		products, err := cfgService.GetProductsByTypeAndSubType(productType, subType)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": products, "count": len(products)})
	}
}

// GetProductsByAssetType 按资产类型查询产品（兼容现有字段）
func GetProductsByAssetType(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		assetTypeStr := c.Param("assetType")
		if assetTypeStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Asset type is required"})
			return
		}

		assetType, err := strconv.Atoi(assetTypeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid asset type format"})
			return
		}

		products, err := cfgService.GetProductsByAssetType(assetType)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": products, "count": len(products)})
	}
}

// CreateProduct 创建产品
func CreateProduct(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		var product models.Product
		if err := c.ShouldBindJSON(&product); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			return
		}

		if err := cfgService.CreateProduct(&product); err != nil {
			if _, ok := err.(validator.ValidationErrors); ok {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed: " + err.Error()})
				return
			}
			if strings.Contains(err.Error(), "validation failed") {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, gin.H{"message": "Product created successfully"})
	}
}

// UpdateProduct 更新产品
func UpdateProduct(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Param("code")
		if code == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product code is required"})
			return
		}

		var product models.Product
		if err := c.ShouldBindJSON(&product); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			return
		}

		// 确保URL中的code与JSON中的code一致
		product.Code = code

		if err := cfgService.UpdateProduct(&product); err != nil {
			if _, ok := err.(validator.ValidationErrors); ok {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed: " + err.Error()})
				return
			}
			if strings.Contains(err.Error(), "validation failed") {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Product updated successfully"})
	}
}

// DeleteProduct 删除产品
func DeleteProduct(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		code := c.Param("code")
		if code == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Product code is required"})
			return
		}

		if err := cfgService.DeleteProduct(code); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Product deleted successfully"})
	}
}

// BatchCreateProducts 批量创建产品
func BatchCreateProducts(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试解析为批量请求格式
		var batchReq models.ProductsBatchReq
		if err := c.ShouldBindJSON(&batchReq); err == nil && len(batchReq.Codes) > 0 {
			// 批量创建（运维格式）
			var failed []string
			for _, code := range batchReq.Codes {
				// 根据Type和SubType获取AssetType
				assetType, err := models.GetAssetTypeFromTypeSubType(batchReq.Type, batchReq.SubType)
				if err != nil {
					failed = append(failed, code+":"+err.Error())
					continue
				}

				p := models.ProductAPI{
					Code:      code,
					SymbolId:  code,
					TradeType: 1,
					TradeMode: 1,
					AssetType: assetType,
					Type:      batchReq.Type,
					SubType:   batchReq.SubType,
					IsActive:  true,
				}
				if err := cfgService.CreateProduct(&p); err != nil {
					failed = append(failed, code+":"+err.Error())
				}
			}
			if len(failed) > 0 {
				c.JSON(http.StatusBadRequest, gin.H{"message": "Some products failed", "failed": failed})
				return
			}
			c.JSON(http.StatusCreated, gin.H{"message": "Products created successfully"})
			return
		}

		// 尝试解析为产品列表格式
		var productsWrapper models.Products
		if err := c.ShouldBindJSON(&productsWrapper); err == nil && len(productsWrapper.Items) > 0 {
			// 批量添加
			var failed []string
			for _, product := range productsWrapper.Items {
				if err := cfgService.CreateProduct(&product); err != nil {
					failed = append(failed, product.Code+":"+err.Error())
				}
			}
			if len(failed) > 0 {
				c.JSON(http.StatusBadRequest, gin.H{"message": "Some products failed", "failed": failed})
				return
			}
			c.JSON(http.StatusCreated, gin.H{"message": "Products created successfully"})
			return
		}

		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
	}
}

// BatchDeleteProducts 批量删除产品
func BatchDeleteProducts(cfgService *service.ConfigService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试解析为批量请求格式
		var batchReq models.ProductsBatchReq
		if err := c.ShouldBindJSON(&batchReq); err == nil && len(batchReq.Codes) > 0 {
			// 批量删除（运维格式）
			var failed []string
			for _, code := range batchReq.Codes {
				if err := cfgService.DeleteProduct(code); err != nil {
					failed = append(failed, code+":"+err.Error())
				}
			}
			if len(failed) > 0 {
				c.JSON(http.StatusInternalServerError, gin.H{"message": "Some products failed to delete", "failed": failed})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Products deleted successfully"})
			return
		}

		// 尝试解析为产品列表格式
		var productsWrapper models.Products
		if err := c.ShouldBindJSON(&productsWrapper); err == nil && len(productsWrapper.Items) > 0 {
			// 批量删除
			var failed []string
			for _, product := range productsWrapper.Items {
				if err := cfgService.DeleteProduct(product.Code); err != nil {
					failed = append(failed, product.Code+":"+err.Error())
				}
			}
			if len(failed) > 0 {
				c.JSON(http.StatusInternalServerError, gin.H{"message": "Some products failed to delete", "failed": failed})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Products deleted successfully"})
			return
		}

		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
	}
}
