package api

import (
	"alltick_conf_manager/models"
	"alltick_conf_manager/service"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/go-playground/validator/v10"
)

func HandleReceivers(cfgService *service.ConfigService) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		receiverID := strings.TrimPrefix(r.URL.Path, "/receivers/")
		switch r.Method {
		case "GET":
			if receiverID != "" && receiverID != "receivers" {
				receiver, err := cfgService.GetReceiver(receiverID)
				if err != nil {
					if strings.Contains(err.Error(), "not found") {
						http.Error(w, err.Error(), http.StatusNotFound)
						return
					}
					http.Error(w, err.Error(), http.StatusInternalServerError)
					return
				}
				json.NewEncoder(w).Encode(receiver)
				return
			}
			receivers, err := cfgService.GetReceivers()
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			json.NewEncoder(w).Encode(receivers)
		case "POST":
			var receiver models.Receiver
			if err := json.NewDecoder(r.Body).Decode(&receiver); err != nil {
				http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
				return
			}
			if err := cfgService.CreateReceiver(&receiver); err != nil {
				if _, ok := err.(validator.ValidationErrors); ok {
					http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
					return
				}
				if strings.Contains(err.Error(), "validation failed") {
					http.Error(w, err.Error(), http.StatusBadRequest)
					return
				}
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			w.WriteHeader(http.StatusCreated)
			json.NewEncoder(w).Encode(map[string]string{"message": "Receiver created successfully"})
		case "PUT":
			var receiver models.Receiver
			if err := json.NewDecoder(r.Body).Decode(&receiver); err != nil {
				http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
				return
			}
			if err := cfgService.UpdateReceiver(&receiver); err != nil {
				if _, ok := err.(validator.ValidationErrors); ok {
					http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
					return
				}
				if strings.Contains(err.Error(), "validation failed") {
					http.Error(w, err.Error(), http.StatusBadRequest)
					return
				}
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			json.NewEncoder(w).Encode(map[string]string{"message": "Receiver updated successfully"})
		case "DELETE":
			if receiverID == "" || receiverID == "receivers" {
				http.Error(w, "Receiver id is required for deletion", http.StatusBadRequest)
				return
			}
			if err := cfgService.DeleteReceiver(receiverID); err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			json.NewEncoder(w).Encode(map[string]string{"message": "Receiver deleted successfully"})
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}
