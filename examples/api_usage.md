# 产品查询 API 使用示例

## 架构说明

本系统实现了API便利性和ETCD存储限制之间的平衡：

### API层（ProductAPI）
- 支持丰富的字段：`Type`、`SubType`、`Description`、`IsActive` 等
- 提供友好的查询接口
- 支持类型验证和自动映射

### 存储层（ProductStorage）
- ETCD中以简化格式存储：`"code:symboldid:tradetype:trademode:assettype"`
- 节省存储空间
- 保持数据一致性

### 类型映射系统
- `Type` + `SubType` ↔ `AssetType` 自动转换
- 支持的类型组合：
  - `forex`: `major`(1), `minor`(2), `exotic`(3)
  - `crypto`: `major`(4), `alt`(5), `stable`(6)
  - `stock`: `us`(7), `hk`(8), `a`(9)
  - `energy`: `oil`(10), `gas`(11), `metal`(12)

## 基本查询

### 1. 获取所有产品
```bash
curl -X GET http://localhost:8080/products
```

### 2. 获取单个产品
```bash
curl -X GET http://localhost:8080/products/EURUSD
```

## 按类型查询

### 3. 按产品类型查询
```bash
# 查询所有外汇产品
curl -X GET http://localhost:8080/products/type/forex

# 查询所有加密货币产品
curl -X GET http://localhost:8080/products/type/crypto

# 查询所有股票产品
curl -X GET http://localhost:8080/products/type/stock
```

### 4. 按类型和子类型查询
```bash
# 查询主要外汇对
curl -X GET http://localhost:8080/products/type/forex/subtype/major

# 查询次要外汇对
curl -X GET http://localhost:8080/products/type/forex/subtype/minor

# 查询主流加密货币
curl -X GET http://localhost:8080/products/type/crypto/subtype/major
```

### 5. 按资产类型查询（兼容现有字段）
```bash
# 查询资产类型为1的产品
curl -X GET http://localhost:8080/products/asset-type/1

# 查询资产类型为2的产品
curl -X GET http://localhost:8080/products/asset-type/2
```

## 产品管理

### 6. 创建产品

#### 方式1：使用Type和SubType（推荐）
```bash
curl -X POST http://localhost:8080/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "EURUSD",
    "symbol_id": "EURUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "type": "forex",
    "sub_type": "major",
    "description": "欧元/美元主要货币对",
    "is_active": true
  }'
```

#### 方式2：直接指定AssetType（兼容模式）
```bash
curl -X POST http://localhost:8080/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "EURUSD",
    "symbol_id": "EURUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "asset_type": 1
  }'
```

**注意：**
- 如果同时提供了`type`/`sub_type`和`asset_type`，系统会优先使用`asset_type`
- 如果只提供`type`/`sub_type`，系统会自动计算对应的`asset_type`
- 数据在ETCD中以简化格式存储：`"EURUSD:EURUSD:1:1:1"`

### 7. 更新产品
```bash
curl -X PUT http://localhost:8080/products/EURUSD \
  -H "Content-Type: application/json" \
  -d '{
    "code": "EURUSD",
    "symbol_id": "EURUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "asset_type": 1,
    "type": "forex",
    "sub_type": "major"
  }'
```

### 8. 删除产品
```bash
curl -X DELETE http://localhost:8080/products/EURUSD
```

## 批量操作

### 9. 批量创建产品（运维格式）

#### 使用Type和SubType（推荐）
```bash
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": "forex",
    "sub_type": "major",
    "codes": ["EURUSD", "GBPUSD", "USDJPY"]
  }'
```

#### 创建不同类型的产品
```bash
# 创建加密货币
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": "crypto",
    "sub_type": "major",
    "codes": ["BTCUSD", "ETHUSD", "ADAUSD"]
  }'

# 创建美股
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": "stock",
    "sub_type": "us",
    "codes": ["AAPL", "GOOGL", "MSFT"]
  }'

# 创建能源产品
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": "energy",
    "sub_type": "oil",
    "codes": ["CRUDE", "BRENT"]
  }'
```

### 10. 批量创建产品（产品列表格式）
```bash
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "code": "EURUSD",
        "symbol_id": "EURUSD",
        "trade_type": 1,
        "trade_mode": 1,
        "asset_type": 1,
        "type": "forex",
        "sub_type": "major"
      },
      {
        "code": "GBPUSD",
        "symbol_id": "GBPUSD",
        "trade_type": 1,
        "trade_mode": 1,
        "asset_type": 1,
        "type": "forex",
        "sub_type": "major"
      }
    ]
  }'
```

### 11. 批量删除产品
```bash
curl -X DELETE http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": 1,
    "codes": ["EURUSD", "GBPUSD", "USDJPY"]
  }'
```

## 响应格式

### 成功响应示例

#### 查询产品响应
```json
{
  "data": [
    {
      "code": "EURUSD",
      "symbol_id": "EURUSD",
      "trade_type": 1,
      "trade_mode": 1,
      "asset_type": 1,
      "type": "forex",
      "sub_type": "major",
      "description": "欧元/美元主要货币对",
      "is_active": true
    }
  ],
  "count": 1
}
```

#### 单个产品响应
```json
{
  "data": {
    "code": "BTCUSD",
    "symbol_id": "BTCUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "asset_type": 4,
    "type": "crypto",
    "sub_type": "major",
    "description": "",
    "is_active": true
  }
}
```

#### 创建/更新成功响应
```json
{
  "message": "Product created successfully"
}
```

### 错误响应示例
```json
{
  "error": "Product not found: INVALID_CODE"
}
```

## 健康检查
```bash
curl -X GET http://localhost:8080/health
```

响应：
```json
{
  "status": "ok",
  "message": "Config service is running"
}
```
