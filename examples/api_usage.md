# 产品查询 API 使用示例

## 基本查询

### 1. 获取所有产品
```bash
curl -X GET http://localhost:8080/products
```

### 2. 获取单个产品
```bash
curl -X GET http://localhost:8080/products/EURUSD
```

## 按类型查询

### 3. 按产品类型查询
```bash
# 查询所有外汇产品
curl -X GET http://localhost:8080/products/type/forex

# 查询所有加密货币产品
curl -X GET http://localhost:8080/products/type/crypto

# 查询所有股票产品
curl -X GET http://localhost:8080/products/type/stock
```

### 4. 按类型和子类型查询
```bash
# 查询主要外汇对
curl -X GET http://localhost:8080/products/type/forex/subtype/major

# 查询次要外汇对
curl -X GET http://localhost:8080/products/type/forex/subtype/minor

# 查询主流加密货币
curl -X GET http://localhost:8080/products/type/crypto/subtype/major
```

### 5. 按资产类型查询（兼容现有字段）
```bash
# 查询资产类型为1的产品
curl -X GET http://localhost:8080/products/asset-type/1

# 查询资产类型为2的产品
curl -X GET http://localhost:8080/products/asset-type/2
```

## 产品管理

### 6. 创建产品
```bash
curl -X POST http://localhost:8080/products \
  -H "Content-Type: application/json" \
  -d '{
    "code": "EURUSD",
    "symbol_id": "EURUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "asset_type": 1,
    "type": "forex",
    "sub_type": "major"
  }'
```

### 7. 更新产品
```bash
curl -X PUT http://localhost:8080/products/EURUSD \
  -H "Content-Type: application/json" \
  -d '{
    "code": "EURUSD",
    "symbol_id": "EURUSD",
    "trade_type": 1,
    "trade_mode": 1,
    "asset_type": 1,
    "type": "forex",
    "sub_type": "major"
  }'
```

### 8. 删除产品
```bash
curl -X DELETE http://localhost:8080/products/EURUSD
```

## 批量操作

### 9. 批量创建产品（运维格式）
```bash
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": 1,
    "codes": ["EURUSD", "GBPUSD", "USDJPY"]
  }'
```

### 10. 批量创建产品（产品列表格式）
```bash
curl -X POST http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "code": "EURUSD",
        "symbol_id": "EURUSD",
        "trade_type": 1,
        "trade_mode": 1,
        "asset_type": 1,
        "type": "forex",
        "sub_type": "major"
      },
      {
        "code": "GBPUSD",
        "symbol_id": "GBPUSD",
        "trade_type": 1,
        "trade_mode": 1,
        "asset_type": 1,
        "type": "forex",
        "sub_type": "major"
      }
    ]
  }'
```

### 11. 批量删除产品
```bash
curl -X DELETE http://localhost:8080/products/batch \
  -H "Content-Type: application/json" \
  -d '{
    "type": 1,
    "codes": ["EURUSD", "GBPUSD", "USDJPY"]
  }'
```

## 响应格式

### 成功响应示例
```json
{
  "data": [
    {
      "code": "EURUSD",
      "symbol_id": "EURUSD",
      "trade_type": 1,
      "trade_mode": 1,
      "asset_type": 1,
      "type": "forex",
      "sub_type": "major"
    }
  ],
  "count": 1
}
```

### 错误响应示例
```json
{
  "error": "Product not found: INVALID_CODE"
}
```

## 健康检查
```bash
curl -X GET http://localhost:8080/health
```

响应：
```json
{
  "status": "ok",
  "message": "Config service is running"
}
```
