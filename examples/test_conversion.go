package main

import (
	"alltick_conf_manager/models"
	"fmt"
	"log"
)

func main() {
	fmt.Println("=== 产品转换测试 ===")

	// 测试1: API格式转存储格式
	fmt.Println("\n1. API格式转存储格式测试:")
	apiProduct := &models.ProductAPI{
		Code:        "EURUSD",
		SymbolId:    "EURUSD",
		TradeType:   1,
		TradeMode:   1,
		Type:        "forex",
		SubType:     "major",
		Description: "欧元/美元主要货币对",
		IsActive:    true,
	}

	fmt.Printf("原始API产品: %+v\n", apiProduct)
	
	storageString := models.ConvertAPIToStorage(apiProduct)
	fmt.Printf("存储格式: %s\n", storageString)

	// 测试2: 存储格式转API格式
	fmt.Println("\n2. 存储格式转API格式测试:")
	convertedAPI, err := models.ConvertStorageToAPI(storageString)
	if err != nil {
		log.Fatalf("转换失败: %v", err)
	}
	fmt.Printf("转换后的API产品: %+v\n", convertedAPI)

	// 测试3: 不同类型的产品
	fmt.Println("\n3. 不同类型产品测试:")
	
	testCases := []struct {
		name    string
		product *models.ProductAPI
	}{
		{
			name: "主流加密货币",
			product: &models.ProductAPI{
				Code:     "BTCUSD",
				SymbolId: "BTCUSD",
				Type:     "crypto",
				SubType:  "major",
			},
		},
		{
			name: "美股",
			product: &models.ProductAPI{
				Code:     "AAPL",
				SymbolId: "AAPL",
				Type:     "stock",
				SubType:  "us",
			},
		},
		{
			name: "原油",
			product: &models.ProductAPI{
				Code:     "CRUDE",
				SymbolId: "CRUDE",
				Type:     "energy",
				SubType:  "oil",
			},
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n%s:\n", tc.name)
		fmt.Printf("  原始: Type=%s, SubType=%s\n", tc.product.Type, tc.product.SubType)
		
		storage := tc.product.ToStorage()
		fmt.Printf("  存储: AssetType=%d\n", storage.AssetType)
		fmt.Printf("  存储字符串: %s\n", storage.ToStorageString())
		
		convertedBack := storage.ToAPI()
		fmt.Printf("  转换回: Type=%s, SubType=%s, AssetType=%d\n", 
			convertedBack.Type, convertedBack.SubType, convertedBack.AssetType)
	}

	// 测试4: 类型验证
	fmt.Println("\n4. 类型验证测试:")
	
	validCases := []struct {
		productType string
		subType     string
	}{
		{"forex", "major"},
		{"forex", "minor"},
		{"crypto", "major"},
		{"stock", "us"},
		{"energy", "oil"},
	}

	invalidCases := []struct {
		productType string
		subType     string
	}{
		{"invalid", "major"},
		{"forex", "invalid"},
		{"", "major"},
	}

	fmt.Println("有效组合:")
	for _, tc := range validCases {
		err := models.ValidateTypeSubType(tc.productType, tc.subType)
		fmt.Printf("  %s/%s: %v\n", tc.productType, tc.subType, err == nil)
	}

	fmt.Println("无效组合:")
	for _, tc := range invalidCases {
		err := models.ValidateTypeSubType(tc.productType, tc.subType)
		fmt.Printf("  %s/%s: %v\n", tc.productType, tc.subType, err != nil)
		if err != nil {
			fmt.Printf("    错误: %v\n", err)
		}
	}

	// 测试5: AssetType映射
	fmt.Println("\n5. AssetType映射测试:")
	for assetType, mapping := range models.AssetTypeToTypeMapping {
		fmt.Printf("  AssetType %d -> Type: %s, SubType: %s\n", 
			assetType, mapping.Type, mapping.SubType)
	}

	fmt.Println("\n=== 测试完成 ===")
}
