package main

import (
	"alltick_conf_manager/api"
	"alltick_conf_manager/etcd"
	"alltick_conf_manager/service"
	"log"

	"github.com/gin-gonic/gin"
)

func main() {
	// 连接 etcd
	etcdClient, err := etcd.NewClient([]string{"localhost:2379"})
	if err != nil {
		log.Fatalf("Failed to connect to etcd: %v", err)
	}
	defer etcdClient.Close()

	// 初始化服务
	configService := service.NewConfigService(etcdClient)

	// 创建 Gin 路由器
	router := gin.Default()

	// 设置产品相关路由
	api.SetupProductRoutes(router, configService)

	// 保持兼容性：设置其他现有的路由
	// 这些可以后续逐步迁移到 Gin
	apiGroup := router.Group("/api/v1")
	{
		apiGroup.GET("/apikeys", gin.WrapF(api.HandleApikeys(configService)))
		apiGroup.POST("/apikeys", gin.WrapF(api.HandleApikeys(configService)))
		apiGroup.PUT("/apikeys", gin.WrapF(api.HandleApikeys(configService)))
		apiGroup.DELETE("/apikeys", gin.WrapF(api.HandleApikeys(configService)))

		apiGroup.GET("/receivers", gin.WrapF(api.HandleReceivers(configService)))
		apiGroup.POST("/receivers", gin.WrapF(api.HandleReceivers(configService)))
		apiGroup.PUT("/receivers", gin.WrapF(api.HandleReceivers(configService)))
		apiGroup.DELETE("/receivers", gin.WrapF(api.HandleReceivers(configService)))

		// 兼容旧的批量产品路由
		apiGroup.POST("/products/batch", gin.WrapF(api.HandleBatchProducts(configService)))
		apiGroup.DELETE("/products/batch", gin.WrapF(api.HandleBatchProducts(configService)))
	}

	// 添加健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Config service is running",
		})
	})

	// 启动服务器
	log.Println("Config service started on :8080 with Gin framework")
	log.Println("Available endpoints:")
	log.Println("  GET    /products                              - 获取所有产品")
	log.Println("  GET    /products/:code                        - 获取单个产品")
	log.Println("  GET    /products/type/:type                   - 按类型查询产品")
	log.Println("  GET    /products/type/:type/subtype/:subtype  - 按类型和子类型查询产品")
	log.Println("  GET    /products/asset-type/:assetType        - 按资产类型查询产品")
	log.Println("  POST   /products                              - 创建产品")
	log.Println("  PUT    /products/:code                        - 更新产品")
	log.Println("  DELETE /products/:code                        - 删除产品")
	log.Println("  POST   /products/batch                        - 批量创建产品")
	log.Println("  DELETE /products/batch                        - 批量删除产品")
	log.Println("  GET    /health                                - 健康检查")

	if err := router.Run(":8080"); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
