package main

import (
	"alltick_conf_manager/api"
	"alltick_conf_manager/etcd"
	"alltick_conf_manager/service"
	"log"
	"net/http"
)

func main1() {
	etcdClient, err := etcd.NewClient([]string{"localhost:2379"})
	if err != nil {
		log.Fatalf("Failed to connect to etcd: %v", err)
	}
	defer etcdClient.Close()
	// 初始化服务
	configService := service.NewConfigService(etcdClient)
	// 注册 API 路由
	http.HandleFunc("/apikeys", api.HandleApikeys(configService))
	http.HandleFunc("/products", api.HandleProducts(configService))
	http.HandleFunc("/receivers", api.HandleReceivers(configService))
	http.HandleFunc("/products/batch", api.HandleBatchProducts(configService))
	// ... 注册其他 API
	log.Println("Config service started on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
